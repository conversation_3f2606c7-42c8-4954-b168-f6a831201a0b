/**
 * 高级网络系统节点集合
 * 提供网络管理、P2P连接、网络同步和网络安全功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 网络连接状态枚举
 */
export enum NetworkConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

/**
 * 网络安全级别枚举
 */
export enum NetworkSecurityLevel {
  NONE = 'none',
  BASIC = 'basic',
  STANDARD = 'standard',
  HIGH = 'high',
  MAXIMUM = 'maximum'
}

/**
 * P2P连接类型枚举
 */
export enum P2PConnectionType {
  DIRECT = 'direct',
  RELAY = 'relay',
  TURN = 'turn',
  STUN = 'stun'
}

/**
 * 网络管理器节点
 * 管理网络连接、配置和状态监控
 */
export class NetworkManagerNode extends VisualScriptNode {
  public static TYPE = 'NetworkManager';
  public static NAME = '网络管理器';
  public static DESCRIPTION = '管理网络连接、配置和状态监控';

  private connections: Map<string, any> = new Map();
  private networkConfig: any = {};
  private isActive: boolean = false;

  constructor() {
    super();
    this.title = NetworkManagerNode.NAME;
    this.category = 'network';
    
    // 输入端口
    this.addInput('enable', '启用', 'boolean');
    this.addInput('config', '配置', 'object');
    this.addInput('connectionId', '连接ID', 'string');
    this.addInput('action', '操作', 'string');
    
    // 输出端口
    this.addOutput('isActive', '是否激活', 'boolean');
    this.addOutput('connections', '连接列表', 'array');
    this.addOutput('status', '状态', 'string');
    this.addOutput('onConnected', '连接成功', 'trigger');
    this.addOutput('onDisconnected', '连接断开', 'trigger');
    this.addOutput('onError', '错误', 'trigger');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable as boolean ?? true;
      const config = inputs?.config as any;
      const connectionId = inputs?.connectionId as string;
      const action = inputs?.action as string;

      if (config) {
        this.networkConfig = { ...this.networkConfig, ...config };
      }

      if (enable && !this.isActive) {
        this.startNetworkManager();
      } else if (!enable && this.isActive) {
        this.stopNetworkManager();
      }

      if (connectionId && action) {
        this.handleConnectionAction(connectionId, action);
      }

      return {
        isActive: this.isActive,
        connections: Array.from(this.connections.values()),
        status: this.getNetworkStatus(),
        onConnected: false,
        onDisconnected: false,
        onError: false
      };

    } catch (error) {
      Debug.error('NetworkManagerNode', '网络管理器操作失败', error);
      return {
        isActive: false,
        connections: [],
        status: 'error',
        onConnected: false,
        onDisconnected: false,
        onError: true
      };
    }
  }

  private startNetworkManager(): void {
    this.isActive = true;
    Debug.log('NetworkManagerNode', '网络管理器已启动');
  }

  private stopNetworkManager(): void {
    this.isActive = false;
    this.connections.clear();
    Debug.log('NetworkManagerNode', '网络管理器已停止');
  }

  private handleConnectionAction(connectionId: string, action: string): void {
    switch (action) {
      case 'add':
        this.connections.set(connectionId, { id: connectionId, state: NetworkConnectionState.CONNECTING });
        break;
      case 'remove':
        this.connections.delete(connectionId);
        break;
      case 'connect':
        if (this.connections.has(connectionId)) {
          this.connections.get(connectionId).state = NetworkConnectionState.CONNECTED;
        }
        break;
      case 'disconnect':
        if (this.connections.has(connectionId)) {
          this.connections.get(connectionId).state = NetworkConnectionState.DISCONNECTED;
        }
        break;
    }
  }

  private getNetworkStatus(): string {
    if (!this.isActive) return 'inactive';
    if (this.connections.size === 0) return 'idle';
    return 'active';
  }
}

/**
 * P2P连接节点
 * 建立和管理点对点网络连接
 */
export class P2PConnectionNode extends VisualScriptNode {
  public static TYPE = 'P2PConnection';
  public static NAME = 'P2P连接';
  public static DESCRIPTION = '建立和管理点对点网络连接';

  private peerConnection: RTCPeerConnection | null = null;
  private dataChannel: RTCDataChannel | null = null;
  private connectionState: NetworkConnectionState = NetworkConnectionState.DISCONNECTED;

  constructor() {
    super();
    this.title = P2PConnectionNode.NAME;
    this.category = 'network';
    
    // 输入端口
    this.addInput('connect', '连接', 'trigger');
    this.addInput('disconnect', '断开', 'trigger');
    this.addInput('peerId', '对等ID', 'string');
    this.addInput('config', '配置', 'object');
    this.addInput('sendData', '发送数据', 'any');
    
    // 输出端口
    this.addOutput('isConnected', '是否连接', 'boolean');
    this.addOutput('connectionState', '连接状态', 'string');
    this.addOutput('receivedData', '接收数据', 'any');
    this.addOutput('onConnected', '连接成功', 'trigger');
    this.addOutput('onDisconnected', '连接断开', 'trigger');
    this.addOutput('onDataReceived', '数据接收', 'trigger');
    this.addOutput('onError', '错误', 'trigger');
  }

  public execute(inputs?: any): any {
    try {
      const connectTrigger = inputs?.connect;
      const disconnectTrigger = inputs?.disconnect;
      const peerId = inputs?.peerId as string;
      const config = inputs?.config as any;
      const sendData = inputs?.sendData;

      if (connectTrigger && peerId) {
        return this.connectToPeer(peerId, config);
      } else if (disconnectTrigger) {
        return this.disconnectFromPeer();
      } else if (sendData !== undefined && this.dataChannel) {
        return this.sendDataToPeer(sendData);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('P2PConnectionNode', 'P2P连接操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private connectToPeer(peerId: string, config?: any): any {
    try {
      const rtcConfig = config || {
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      };

      this.peerConnection = new RTCPeerConnection(rtcConfig);
      this.connectionState = NetworkConnectionState.CONNECTING;

      // 创建数据通道
      this.dataChannel = this.peerConnection.createDataChannel('data', {
        ordered: true
      });

      this.setupDataChannelHandlers();
      this.setupPeerConnectionHandlers();

      Debug.log('P2PConnectionNode', `开始连接到对等节点: ${peerId}`);

      return {
        ...this.getDefaultOutputs(),
        connectionState: this.connectionState,
        onConnected: false
      };

    } catch (error) {
      Debug.error('P2PConnectionNode', 'P2P连接失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private disconnectFromPeer(): any {
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
    if (this.dataChannel) {
      this.dataChannel.close();
      this.dataChannel = null;
    }
    this.connectionState = NetworkConnectionState.DISCONNECTED;

    return {
      ...this.getDefaultOutputs(),
      connectionState: this.connectionState,
      onDisconnected: true
    };
  }

  private sendDataToPeer(data: any): any {
    if (this.dataChannel && this.dataChannel.readyState === 'open') {
      this.dataChannel.send(JSON.stringify(data));
      return {
        ...this.getDefaultOutputs(),
        connectionState: this.connectionState
      };
    }

    return {
      ...this.getDefaultOutputs(),
      onError: true
    };
  }

  private setupDataChannelHandlers(): void {
    if (!this.dataChannel) return;

    this.dataChannel.onopen = () => {
      this.connectionState = NetworkConnectionState.CONNECTED;
      Debug.log('P2PConnectionNode', 'P2P数据通道已打开');
    };

    this.dataChannel.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        Debug.log('P2PConnectionNode', '接收到P2P数据', data);
      } catch (error) {
        Debug.error('P2PConnectionNode', '解析P2P数据失败', error);
      }
    };

    this.dataChannel.onclose = () => {
      this.connectionState = NetworkConnectionState.DISCONNECTED;
      Debug.log('P2PConnectionNode', 'P2P数据通道已关闭');
    };

    this.dataChannel.onerror = (error) => {
      this.connectionState = NetworkConnectionState.ERROR;
      Debug.error('P2PConnectionNode', 'P2P数据通道错误', error);
    };
  }

  private setupPeerConnectionHandlers(): void {
    if (!this.peerConnection) return;

    this.peerConnection.onconnectionstatechange = () => {
      if (this.peerConnection) {
        const state = this.peerConnection.connectionState;
        Debug.log('P2PConnectionNode', `P2P连接状态变化: ${state}`);
        
        switch (state) {
          case 'connected':
            this.connectionState = NetworkConnectionState.CONNECTED;
            break;
          case 'disconnected':
            this.connectionState = NetworkConnectionState.DISCONNECTED;
            break;
          case 'failed':
            this.connectionState = NetworkConnectionState.ERROR;
            break;
        }
      }
    };
  }

  private getDefaultOutputs(): any {
    return {
      isConnected: this.connectionState === NetworkConnectionState.CONNECTED,
      connectionState: this.connectionState,
      receivedData: null,
      onConnected: false,
      onDisconnected: false,
      onDataReceived: false,
      onError: false
    };
  }
}

/**
 * 网络同步节点
 * 同步网络数据和状态信息
 */
export class NetworkSyncNode extends VisualScriptNode {
  public static TYPE = 'NetworkSync';
  public static NAME = '网络同步';
  public static DESCRIPTION = '同步网络数据和状态信息';

  private syncData: Map<string, any> = new Map();
  private syncInterval: number | null = null;
  private isActive: boolean = false;

  constructor() {
    super();
    this.title = NetworkSyncNode.NAME;
    this.category = 'network';

    // 输入端口
    this.addInput('enable', '启用', 'boolean');
    this.addInput('syncKey', '同步键', 'string');
    this.addInput('syncValue', '同步值', 'any');
    this.addInput('interval', '同步间隔', 'number');
    this.addInput('forceSync', '强制同步', 'trigger');

    // 输出端口
    this.addOutput('isActive', '是否激活', 'boolean');
    this.addOutput('syncedData', '同步数据', 'object');
    this.addOutput('lastSyncTime', '最后同步时间', 'number');
    this.addOutput('onSynced', '同步完成', 'trigger');
    this.addOutput('onError', '错误', 'trigger');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable as boolean ?? true;
      const syncKey = inputs?.syncKey as string;
      const syncValue = inputs?.syncValue;
      const interval = inputs?.interval as number ?? 1000;
      const forceSync = inputs?.forceSync;

      if (enable && !this.isActive) {
        this.startSync(interval);
      } else if (!enable && this.isActive) {
        this.stopSync();
      }

      if (syncKey && syncValue !== undefined) {
        this.setSyncData(syncKey, syncValue);
      }

      if (forceSync) {
        return this.performSync();
      }

      return {
        isActive: this.isActive,
        syncedData: Object.fromEntries(this.syncData),
        lastSyncTime: Date.now(),
        onSynced: false,
        onError: false
      };

    } catch (error) {
      Debug.error('NetworkSyncNode', '网络同步操作失败', error);
      return {
        isActive: false,
        syncedData: {},
        lastSyncTime: 0,
        onSynced: false,
        onError: true
      };
    }
  }

  private startSync(interval: number): void {
    this.isActive = true;
    this.syncInterval = window.setInterval(() => {
      this.performSync();
    }, interval);
    Debug.log('NetworkSyncNode', `网络同步已启动，间隔: ${interval}ms`);
  }

  private stopSync(): void {
    this.isActive = false;
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    Debug.log('NetworkSyncNode', '网络同步已停止');
  }

  private setSyncData(key: string, value: any): void {
    this.syncData.set(key, value);
    Debug.log('NetworkSyncNode', `设置同步数据: ${key} = ${value}`);
  }

  private performSync(): any {
    try {
      // 模拟网络同步操作
      const syncedData = Object.fromEntries(this.syncData);
      Debug.log('NetworkSyncNode', '执行网络同步', syncedData);

      return {
        isActive: this.isActive,
        syncedData,
        lastSyncTime: Date.now(),
        onSynced: true,
        onError: false
      };

    } catch (error) {
      Debug.error('NetworkSyncNode', '网络同步失败', error);
      return {
        isActive: this.isActive,
        syncedData: {},
        lastSyncTime: Date.now(),
        onSynced: false,
        onError: true
      };
    }
  }
}

/**
 * 网络安全节点
 * 提供网络安全和加密功能
 */
export class NetworkSecurityNode extends VisualScriptNode {
  public static TYPE = 'NetworkSecurity';
  public static NAME = '网络安全';
  public static DESCRIPTION = '提供网络安全和加密功能';

  private securityLevel: NetworkSecurityLevel = NetworkSecurityLevel.STANDARD;
  private encryptionKey: string = '';
  private isSecured: boolean = false;

  constructor() {
    super();
    this.title = NetworkSecurityNode.NAME;
    this.category = 'network';

    // 输入端口
    this.addInput('enable', '启用', 'boolean');
    this.addInput('securityLevel', '安全级别', 'string');
    this.addInput('encryptionKey', '加密密钥', 'string');
    this.addInput('dataToEncrypt', '待加密数据', 'any');
    this.addInput('dataToDecrypt', '待解密数据', 'string');

    // 输出端口
    this.addOutput('isSecured', '是否安全', 'boolean');
    this.addOutput('securityLevel', '安全级别', 'string');
    this.addOutput('encryptedData', '加密数据', 'string');
    this.addOutput('decryptedData', '解密数据', 'any');
    this.addOutput('onSecured', '安全启用', 'trigger');
    this.addOutput('onError', '错误', 'trigger');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable as boolean ?? true;
      const securityLevel = inputs?.securityLevel as string;
      const encryptionKey = inputs?.encryptionKey as string;
      const dataToEncrypt = inputs?.dataToEncrypt;
      const dataToDecrypt = inputs?.dataToDecrypt as string;

      if (securityLevel) {
        this.securityLevel = securityLevel as NetworkSecurityLevel;
      }

      if (encryptionKey) {
        this.encryptionKey = encryptionKey;
      }

      if (enable && !this.isSecured) {
        this.enableSecurity();
      } else if (!enable && this.isSecured) {
        this.disableSecurity();
      }

      let encryptedData = '';
      let decryptedData: any = null;

      if (dataToEncrypt !== undefined) {
        encryptedData = this.encryptData(dataToEncrypt);
      }

      if (dataToDecrypt) {
        decryptedData = this.decryptData(dataToDecrypt);
      }

      return {
        isSecured: this.isSecured,
        securityLevel: this.securityLevel,
        encryptedData,
        decryptedData,
        onSecured: false,
        onError: false
      };

    } catch (error) {
      Debug.error('NetworkSecurityNode', '网络安全操作失败', error);
      return {
        isSecured: false,
        securityLevel: this.securityLevel,
        encryptedData: '',
        decryptedData: null,
        onSecured: false,
        onError: true
      };
    }
  }

  private enableSecurity(): void {
    this.isSecured = true;
    Debug.log('NetworkSecurityNode', `网络安全已启用，级别: ${this.securityLevel}`);
  }

  private disableSecurity(): void {
    this.isSecured = false;
    Debug.log('NetworkSecurityNode', '网络安全已禁用');
  }

  private encryptData(data: any): string {
    try {
      // 简单的Base64编码作为示例（实际应用中应使用更强的加密算法）
      const jsonData = JSON.stringify(data);
      const encrypted = btoa(jsonData + this.encryptionKey);
      Debug.log('NetworkSecurityNode', '数据加密完成');
      return encrypted;
    } catch (error) {
      Debug.error('NetworkSecurityNode', '数据加密失败', error);
      return '';
    }
  }

  private decryptData(encryptedData: string): any {
    try {
      // 简单的Base64解码作为示例
      const decrypted = atob(encryptedData);
      const jsonData = decrypted.replace(this.encryptionKey, '');
      const data = JSON.parse(jsonData);
      Debug.log('NetworkSecurityNode', '数据解密完成');
      return data;
    } catch (error) {
      Debug.error('NetworkSecurityNode', '数据解密失败', error);
      return null;
    }
  }
}
