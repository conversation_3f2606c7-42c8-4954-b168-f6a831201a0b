/**
 * 测试批次R1：物理与动画系统节点注册
 * 验证40个节点的注册状态
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 测试批次R1：物理与动画系统节点注册');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

try {
  // 1. 检查注册表文件是否存在
  const registryPath = path.join(__dirname, 'PhysicsAnimationNodesRegistry.ts');
  if (fs.existsSync(registryPath)) {
    console.log('✅ PhysicsAnimationNodesRegistry.ts 文件存在');
  } else {
    console.log('❌ PhysicsAnimationNodesRegistry.ts 文件不存在');
    process.exit(1);
  }

  // 2. 读取文件内容
  const registryContent = fs.readFileSync(registryPath, 'utf8');
  console.log(`✅ 文件大小: ${registryContent.length} 字符`);

  // 3. 检查关键更新
  const requiredUpdates = [
    '注册批次R1：物理与动画系统节点注册表（已完成）',
    '总计40个节点',
    'registerAudioNodes',
    '音频系统节点（12个）',
    'audioNodes: 12'
  ];

  console.log('\n🔍 检查关键更新:');
  let foundUpdates = 0;
  for (const update of requiredUpdates) {
    if (registryContent.includes(update)) {
      console.log(`  ✅ ${update}`);
      foundUpdates++;
    } else {
      console.log(`  ❌ ${update}`);
    }
  }

  console.log(`\n📊 更新检查: ${foundUpdates}/${requiredUpdates.length} 个更新已完成`);

  // 4. 检查音频节点类型
  const audioNodeTypes = [
    '3DAudio',
    'AudioFilter', 
    'AudioEffect',
    'AudioMixer',
    'AudioAnalyzer',
    'AudioCompression',
    'AudioEqualizer',
    'AudioVisualization',
    'Reverb',
    'Echo',
    'AudioRecorder',
    'AudioStreaming'
  ];

  console.log('\n🔊 检查音频节点类型:');
  let foundAudioNodes = 0;
  for (const nodeType of audioNodeTypes) {
    if (registryContent.includes(`'${nodeType}'`)) {
      console.log(`  ✅ ${nodeType}`);
      foundAudioNodes++;
    } else {
      console.log(`  ❌ ${nodeType}`);
    }
  }

  console.log(`\n📈 音频节点检查: ${foundAudioNodes}/${audioNodeTypes.length} 个音频节点已定义`);

  // 5. 检查音频节点实现文件
  const audioNodesPath = path.join(__dirname, '../nodes/audio/AdvancedAudioSystemNodes.ts');
  if (fs.existsSync(audioNodesPath)) {
    console.log('\n✅ AdvancedAudioSystemNodes.ts 文件存在');
    
    const audioNodesContent = fs.readFileSync(audioNodesPath, 'utf8');
    
    // 检查新增的音频节点类
    const audioNodeClasses = [
      'AudioAnalyzerNode',
      'AudioCompressionNode', 
      'AudioEqualizerNode',
      'AudioVisualizationNode',
      'EchoNode',
      'AudioRecorderNode',
      'AudioStreamingNode'
    ];

    console.log('\n🎵 检查音频节点类实现:');
    let foundAudioClasses = 0;
    for (const nodeClass of audioNodeClasses) {
      if (audioNodesContent.includes(`export class ${nodeClass}`)) {
        console.log(`  ✅ ${nodeClass}`);
        foundAudioClasses++;
      } else {
        console.log(`  ❌ ${nodeClass}`);
      }
    }

    console.log(`\n📊 音频节点类检查: ${foundAudioClasses}/${audioNodeClasses.length} 个音频节点类已实现`);
  } else {
    console.log('\n❌ AdvancedAudioSystemNodes.ts 文件不存在');
  }

  // 6. 总结
  console.log('\n📋 批次R1注册状态总结:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('🔧 物理系统节点: 17个 (已注册)');
  console.log('🎬 动画系统节点: 11个 (已注册)');
  console.log('🔊 音频系统节点: 12个 (新增)');
  console.log('📈 总计: 40个节点');
  console.log('');
  
  if (foundUpdates === requiredUpdates.length && foundAudioNodes === audioNodeTypes.length) {
    console.log('✅ 批次R1：物理与动画系统节点注册完成');
    console.log('   所有40个节点已成功注册到系统');
  } else {
    console.log('⚠️ 批次R1注册存在问题，请检查上述失败项');
  }

} catch (error) {
  console.error('❌ 测试过程中发生错误:', error.message);
  process.exit(1);
}
