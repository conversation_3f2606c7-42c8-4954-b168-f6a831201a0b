/**
 * 注册批次R2：高级系统节点注册表（已完成）
 * 注册网络系统节点（4个）、输入系统节点（5个）、UI系统节点（3个）和专业应用节点（20个）到编辑器
 * 总计32个节点
 */

import { NodeRegistry } from './NodeRegistry';

// 导入网络系统节点（4个）
import {
  NetworkManagerNode,
  P2PConnectionNode,
  NetworkSyncNode,
  NetworkSecurityNode
} from '../nodes/network/AdvancedNetworkNodes';

// 导入输入系统节点（5个）
import {
  TouchInputNode,
  GamepadInputNode,
  KeyboardInputNode,
  MouseInputNode,
  CustomInputNode
} from '../nodes/input/AdvancedInputSystemNodes';

// 导入UI系统节点（3个）
import {
  UILayoutNode,
  UIAnimationNode,
  UIEventNode
} from '../nodes/ui/AdvancedUISystemNodes';

// 导入专业应用节点（20个）
import {
  GISDataLoaderNode,
  CoordinateTransformNode,
  SpatialQueryNode,
  GeofencingNode,
  RouteCalculationNode,
  LocationServicesNode,
  MapRenderingNode,
  SpatialAnalysisNode,
  GeospatialVisualizationNode,
  TerrainAnalysisNode,
  WeatherDataNode,
  SatelliteImageryNode,
  GPSTrackingNode,
  NavigationNode,
  LandmarkDetectionNode,
  UrbanPlanningNode,
  EnvironmentalMonitoringNode,
  DisasterManagementNode,
  SmartCityNode,
  ProfessionalApplicationNode
} from '../nodes/spatial/ProfessionalSpatialNodes';

/**
 * 高级系统节点注册表
 */
export class AdvancedSystemNodesRegistry {
  private nodeRegistry: NodeRegistry;
  private registered: boolean = false;

  constructor(nodeRegistry: NodeRegistry) {
    this.nodeRegistry = nodeRegistry;
  }

  /**
   * 注册所有高级系统节点
   */
  public registerAll(): void {
    if (this.registered) {
      console.warn('高级系统节点已经注册过了');
      return;
    }

    console.log('开始注册批次R2：高级系统节点（32个）...');

    try {
      // 注册网络系统节点（4个）
      this.registerNetworkNodes();

      // 注册输入系统节点（5个）
      this.registerInputSystemNodes();

      // 注册UI系统节点（3个）
      this.registerUISystemNodes();

      // 注册专业应用节点（20个）
      this.registerProfessionalApplicationNodes();

      this.registered = true;
      console.log('✅ 批次R2高级系统节点注册完成 - 32个节点');

      // 输出注册统计
      this.logRegistrationStats();

    } catch (error) {
      console.error('❌ 批次R2高级系统节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册网络系统节点（4个）
   */
  private registerNetworkNodes(): void {
    console.log('注册网络系统节点...');

    const networkNodes = [
      { 
        class: NetworkManagerNode, 
        type: 'NetworkManager', 
        name: '网络管理器',
        desc: '管理网络连接、配置和状态监控'
      },
      { 
        class: P2PConnectionNode, 
        type: 'P2PConnection', 
        name: 'P2P连接',
        desc: '建立和管理点对点网络连接'
      },
      { 
        class: NetworkSyncNode, 
        type: 'NetworkSync', 
        name: '网络同步',
        desc: '同步网络数据和状态信息'
      },
      { 
        class: NetworkSecurityNode, 
        type: 'NetworkSecurity', 
        name: '网络安全',
        desc: '提供网络安全和加密功能'
      }
    ];

    networkNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        name,
        desc,
        'network',
        () => new NodeClass(),
        {
          category: 'network',
          tags: ['网络', '通信', '连接'],
          version: '1.0.0',
          author: 'DL Engine Team'
        }
      );
    });

    console.log(`✅ 网络系统节点注册完成 - ${networkNodes.length}个节点`);
  }

  /**
   * 注册输入系统节点（5个）
   */
  private registerInputSystemNodes(): void {
    console.log('注册输入系统节点...');

    const inputNodes = [
      { 
        class: TouchInputNode, 
        type: 'TouchInput', 
        name: '触摸输入',
        desc: '处理触摸屏输入和手势识别'
      },
      { 
        class: GamepadInputNode, 
        type: 'GamepadInput', 
        name: '手柄输入',
        desc: '处理游戏手柄输入和控制'
      },
      { 
        class: KeyboardInputNode, 
        type: 'KeyboardInput', 
        name: '键盘输入',
        desc: '处理键盘按键输入和快捷键'
      },
      { 
        class: MouseInputNode, 
        type: 'MouseInput', 
        name: '鼠标输入',
        desc: '处理鼠标点击、移动和滚轮输入'
      },
      { 
        class: CustomInputNode, 
        type: 'CustomInput', 
        name: '自定义输入',
        desc: '创建和处理自定义输入设备'
      }
    ];

    inputNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        name,
        desc,
        'input',
        () => new NodeClass(),
        {
          category: 'input',
          tags: ['输入', '交互', '控制'],
          version: '1.0.0',
          author: 'DL Engine Team'
        }
      );
    });

    console.log(`✅ 输入系统节点注册完成 - ${inputNodes.length}个节点`);
  }

  /**
   * 注册UI系统节点（3个）
   */
  private registerUISystemNodes(): void {
    console.log('注册UI系统节点...');

    const uiNodes = [
      { 
        class: UILayoutNode, 
        type: 'UILayout', 
        name: 'UI布局',
        desc: '管理UI元素的布局和排列'
      },
      { 
        class: UIAnimationNode, 
        type: 'UIAnimation', 
        name: 'UI动画',
        desc: '创建和控制UI元素动画效果'
      },
      { 
        class: UIEventNode, 
        type: 'UIEvent', 
        name: 'UI事件',
        desc: '处理UI交互事件和响应'
      }
    ];

    uiNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        name,
        desc,
        'ui',
        () => new NodeClass(),
        {
          category: 'ui',
          tags: ['UI', '界面', '交互'],
          version: '1.0.0',
          author: 'DL Engine Team'
        }
      );
    });

    console.log(`✅ UI系统节点注册完成 - ${uiNodes.length}个节点`);
  }

  /**
   * 注册专业应用节点（20个）
   */
  private registerProfessionalApplicationNodes(): void {
    console.log('注册专业应用节点...');

    const professionalNodes = [
      { class: GISDataLoaderNode, type: 'GISDataLoader', name: 'GIS数据加载', desc: '加载和处理GIS地理信息数据' },
      { class: CoordinateTransformNode, type: 'CoordinateTransform', name: '坐标转换', desc: '转换不同坐标系统之间的坐标' },
      { class: SpatialQueryNode, type: 'SpatialQuery', name: '空间查询', desc: '执行空间数据查询和分析' },
      { class: GeofencingNode, type: 'Geofencing', name: '地理围栏', desc: '创建和管理地理围栏区域' },
      { class: RouteCalculationNode, type: 'RouteCalculation', name: '路径计算', desc: '计算最优路径和导航路线' },
      { class: LocationServicesNode, type: 'LocationServices', name: '位置服务', desc: '提供位置定位和跟踪服务' },
      { class: MapRenderingNode, type: 'MapRendering', name: '地图渲染', desc: '渲染和显示地图数据' },
      { class: SpatialAnalysisNode, type: 'SpatialAnalysis', name: '空间分析', desc: '执行复杂的空间数据分析' },
      { class: GeospatialVisualizationNode, type: 'GeospatialVisualization', name: '地理空间可视化', desc: '可视化地理空间数据' },
      { class: TerrainAnalysisNode, type: 'TerrainAnalysis', name: '地形分析', desc: '分析地形数据和特征' },
      { class: WeatherDataNode, type: 'WeatherData', name: '天气数据', desc: '获取和处理天气信息' },
      { class: SatelliteImageryNode, type: 'SatelliteImagery', name: '卫星影像', desc: '处理卫星图像数据' },
      { class: GPSTrackingNode, type: 'GPSTracking', name: 'GPS追踪', desc: 'GPS定位和轨迹追踪' },
      { class: NavigationNode, type: 'Navigation', name: '导航', desc: '提供导航和路线指引' },
      { class: LandmarkDetectionNode, type: 'LandmarkDetection', name: '地标检测', desc: '识别和检测地理地标' },
      { class: UrbanPlanningNode, type: 'UrbanPlanning', name: '城市规划', desc: '支持城市规划和设计' },
      { class: EnvironmentalMonitoringNode, type: 'EnvironmentalMonitoring', name: '环境监测', desc: '监测环境数据和变化' },
      { class: DisasterManagementNode, type: 'DisasterManagement', name: '灾害管理', desc: '灾害预警和应急管理' },
      { class: SmartCityNode, type: 'SmartCity', name: '智慧城市', desc: '智慧城市系统集成' },
      { class: ProfessionalApplicationNode, type: 'ProfessionalApplication', name: '专业应用', desc: '其他专业应用功能' }
    ];

    professionalNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        name,
        desc,
        'spatial',
        () => new NodeClass(),
        {
          category: 'spatial',
          tags: ['空间', 'GIS', '地理', '专业'],
          version: '1.0.0',
          author: 'DL Engine Team'
        }
      );
    });

    console.log(`✅ 专业应用节点注册完成 - ${professionalNodes.length}个节点`);
  }

  /**
   * 输出注册统计信息
   */
  private logRegistrationStats(): void {
    console.log('\n📊 批次R2高级系统节点注册统计:');
    console.log('├─ 网络系统节点: 4个');
    console.log('├─ 输入系统节点: 5个');
    console.log('├─ UI系统节点: 3个');
    console.log('├─ 专业应用节点: 20个');
    console.log('└─ 总计: 32个节点');
    console.log('\n✅ 所有高级系统节点已成功注册到编辑器');
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 网络系统节点
      'NetworkManager',
      'P2PConnection',
      'NetworkSync',
      'NetworkSecurity',

      // 输入系统节点
      'TouchInput',
      'GamepadInput',
      'KeyboardInput',
      'MouseInput',
      'CustomInput',

      // UI系统节点
      'UILayout',
      'UIAnimation',
      'UIEvent',

      // 专业应用节点
      'GISDataLoader',
      'CoordinateTransform',
      'SpatialQuery',
      'Geofencing',
      'RouteCalculation',
      'LocationServices',
      'MapRendering',
      'SpatialAnalysis',
      'GeospatialVisualization',
      'TerrainAnalysis',
      'WeatherData',
      'SatelliteImagery',
      'GPSTracking',
      'Navigation',
      'LandmarkDetection',
      'UrbanPlanning',
      'EnvironmentalMonitoring',
      'DisasterManagement',
      'SmartCity',
      'ProfessionalApplication'
    ];
  }

  /**
   * 检查节点是否已注册
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 获取注册的节点数量
   */
  public getRegisteredNodeCount(): number {
    return this.getAllRegisteredNodeTypes().length;
  }
}

// 导出注册表实例
export const advancedSystemNodesRegistry = new AdvancedSystemNodesRegistry(NodeRegistry.getInstance());

// 导出注册函数
export function registerAdvancedSystemNodes(): void {
  advancedSystemNodesRegistry.registerAll();
}

// 导出验证函数
export function validateAdvancedSystemNodes(): boolean {
  const nodeTypes = advancedSystemNodesRegistry.getAllRegisteredNodeTypes();
  const registry = NodeRegistry.getInstance();

  for (const nodeType of nodeTypes) {
    if (!registry.hasNode(nodeType)) {
      console.error(`❌ 节点类型 ${nodeType} 未找到`);
      return false;
    }
  }

  console.log('✅ 所有高级系统节点验证通过');
  return true;
}
