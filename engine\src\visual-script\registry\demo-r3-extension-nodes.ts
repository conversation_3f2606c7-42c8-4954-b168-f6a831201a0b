/**
 * 批次R3扩展功能节点演示
 * 演示30个扩展功能节点的注册和使用
 */

import { Debug } from '../../utils/Debug';
import { extensionNodesRegistry } from './ExtensionNodesRegistry';

// 导入节点类进行演示
import { VRControllerInputNode, ARTouchInputNode } from '../nodes/input/VRARInputNodes';
import { MultiTouchGestureNode, PressureSensitiveInputNode } from '../nodes/input/AdvancedInputNodes';
import { AccelerometerNode, GyroscopeNode } from '../nodes/input/SensorInputNodes';
import { PaymentGatewayNode, SubscriptionNode } from '../nodes/batch34/PaymentNodes';
import { GoogleServicesNode, CloudStorageNode } from '../nodes/batch34/ThirdPartyNodes';

/**
 * 演示批次R3节点注册
 */
export function demonstrateR3NodesRegistration(): void {
  Debug.log('Demo', '=== 批次R3扩展功能节点注册演示 ===');

  try {
    // 执行注册
    const startTime = Date.now();
    extensionNodesRegistry.registerAllNodes();
    const endTime = Date.now();

    // 获取注册统计
    const stats = extensionNodesRegistry.getRegistrationStats();
    
    Debug.log('Demo', '注册完成:', {
      totalNodes: stats.totalNodes,
      registrationTime: `${endTime - startTime}ms`,
      isComplete: stats.isComplete
    });

    // 显示各分类节点数量
    Debug.log('Demo', '节点分类统计:', {
      'VR/AR输入节点': stats.vrArInputNodes,
      '高级输入节点': stats.advancedInputNodes,
      '传感器输入节点': stats.sensorInputNodes,
      '支付系统节点': stats.paymentNodes,
      '第三方集成节点': stats.integrationNodes
    });

    // 显示已注册的节点类型
    const registeredTypes = extensionNodesRegistry.getAllRegisteredNodeTypes();
    Debug.log('Demo', `已注册${registeredTypes.length}个节点类型:`, registeredTypes);

  } catch (error) {
    Debug.error('Demo', '注册演示失败:', error);
  }
}

/**
 * 演示VR/AR输入节点
 */
export function demonstrateVRARInputNodes(): void {
  Debug.log('Demo', '=== VR/AR输入节点演示 ===');

  // VR控制器输入演示
  const vrController = new VRControllerInputNode();
  const vrResult = vrController.execute({
    enable: true,
    controllerId: 'right',
    hapticIntensity: 0.5,
    hapticDuration: 200
  });
  
  Debug.log('Demo', 'VR控制器输入结果:', {
    position: vrResult.position,
    rotation: vrResult.rotation,
    connected: vrResult.connected,
    triggerValue: vrResult.triggerValue,
    buttonA: vrResult.buttonA
  });

  // AR触摸输入演示
  const arTouch = new ARTouchInputNode();
  const arResult = arTouch.execute({
    enable: true,
    raycastDistance: 10,
    touchSensitivity: 1.0
  });
  
  Debug.log('Demo', 'AR触摸输入结果:', {
    isTouching: arResult.isTouching,
    touchPosition: arResult.touchPosition,
    worldPosition: arResult.worldPosition,
    hitObject: arResult.hitObject
  });
}

/**
 * 演示高级输入节点
 */
export function demonstrateAdvancedInputNodes(): void {
  Debug.log('Demo', '=== 高级输入节点演示 ===');

  // 多点触控手势演示
  const multiTouch = new MultiTouchGestureNode();
  const gestureResult = multiTouch.execute({
    enable: true,
    sensitivity: 1.0,
    minConfidence: 0.7,
    gestureTypes: ['tap', 'pinch', 'swipe', 'rotate']
  });
  
  Debug.log('Demo', '多点触控手势结果:', {
    gestureType: gestureResult.gestureType,
    confidence: gestureResult.confidence,
    touchPoints: gestureResult.touchPoints,
    gestureData: gestureResult.gestureData
  });

  // 压感输入演示
  const pressureInput = new PressureSensitiveInputNode();
  const pressureResult = pressureInput.execute({
    enable: true,
    sensitivity: 1.0,
    threshold: 0.5
  });
  
  Debug.log('Demo', '压感输入结果:', {
    pressure: pressureResult.pressure,
    isPressed: pressureResult.isPressed,
    pressureLevel: pressureResult.pressureLevel,
    maxPressure: pressureResult.maxPressure
  });
}

/**
 * 演示传感器输入节点
 */
export function demonstrateSensorInputNodes(): void {
  Debug.log('Demo', '=== 传感器输入节点演示 ===');

  // 加速度计演示
  const accelerometer = new AccelerometerNode();
  const accelResult = accelerometer.execute({
    enable: true,
    includeGravity: true,
    sensitivity: 1.0,
    threshold: 0.1
  });
  
  Debug.log('Demo', '加速度计结果:', {
    acceleration: accelResult.acceleration,
    magnitude: accelResult.magnitude,
    onShake: accelResult.onShake,
    onTilt: accelResult.onTilt
  });

  // 陀螺仪演示
  const gyroscope = new GyroscopeNode();
  const gyroResult = gyroscope.execute({
    enable: true,
    sensitivity: 1.0,
    smoothing: 0.1
  });
  
  Debug.log('Demo', '陀螺仪结果:', {
    rotation: gyroResult.rotation,
    alpha: gyroResult.alpha,
    beta: gyroResult.beta,
    gamma: gyroResult.gamma,
    onRotation: gyroResult.onRotation
  });
}

/**
 * 演示支付系统节点
 */
export function demonstratePaymentNodes(): void {
  Debug.log('Demo', '=== 支付系统节点演示 ===');

  // 支付网关演示
  const paymentGateway = new PaymentGatewayNode();
  const paymentResult = paymentGateway.execute({
    amount: 99.99,
    currency: 'USD',
    paymentMethod: 'credit_card',
    merchantId: 'test_merchant'
  });
  
  Debug.log('Demo', '支付网关结果:', {
    transactionId: paymentResult.transactionId,
    status: paymentResult.status,
    amount: paymentResult.amount,
    currency: paymentResult.currency
  });

  // 订阅系统演示
  const subscription = new SubscriptionNode();
  const subResult = subscription.execute({
    planId: 'premium_monthly',
    userId: 'user_123',
    paymentMethod: 'credit_card'
  });
  
  Debug.log('Demo', '订阅系统结果:', {
    subscriptionId: subResult.subscriptionId,
    status: subResult.status,
    planId: subResult.planId,
    nextBillingDate: subResult.nextBillingDate
  });
}

/**
 * 演示第三方集成节点
 */
export function demonstrateIntegrationNodes(): void {
  Debug.log('Demo', '=== 第三方集成节点演示 ===');

  // Google服务演示
  const googleServices = new GoogleServicesNode();
  const googleResult = googleServices.execute({
    service: 'drive',
    operation: 'list_files',
    credentials: 'test_credentials'
  });
  
  Debug.log('Demo', 'Google服务结果:', {
    service: googleResult.service,
    operation: googleResult.operation,
    status: googleResult.status,
    data: googleResult.data
  });

  // 云存储演示
  const cloudStorage = new CloudStorageNode();
  const storageResult = cloudStorage.execute({
    provider: 'aws_s3',
    operation: 'upload',
    fileName: 'test.jpg',
    bucket: 'my-bucket'
  });
  
  Debug.log('Demo', '云存储结果:', {
    provider: storageResult.provider,
    operation: storageResult.operation,
    status: storageResult.status,
    url: storageResult.url
  });
}

/**
 * 运行所有演示
 */
export function runAllR3Demonstrations(): void {
  Debug.log('Demo', '🚀 开始运行批次R3扩展功能节点全部演示');
  
  try {
    demonstrateR3NodesRegistration();
    demonstrateVRARInputNodes();
    demonstrateAdvancedInputNodes();
    demonstrateSensorInputNodes();
    demonstratePaymentNodes();
    demonstrateIntegrationNodes();
    
    Debug.log('Demo', '🎉 所有批次R3演示完成！');
  } catch (error) {
    Debug.error('Demo', '演示过程中发生错误:', error);
  }
}

// 如果直接运行此文件，执行演示
if (require.main === module) {
  runAllR3Demonstrations();
}
