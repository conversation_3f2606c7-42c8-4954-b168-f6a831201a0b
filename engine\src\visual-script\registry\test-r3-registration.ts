/**
 * 批次R3扩展功能节点注册测试
 * 测试30个扩展功能节点的注册功能
 */

import { Debug } from '../../utils/Debug';
import { extensionNodesRegistry, ExtensionNodesRegistry } from './ExtensionNodesRegistry';

/**
 * 测试批次R3节点注册
 */
export function testR3NodesRegistration(): void {
  Debug.log('Test', '=== 开始测试批次R3扩展功能节点注册 ===');

  try {
    // 重置注册状态
    extensionNodesRegistry.resetRegistration();
    
    // 执行注册
    const startTime = Date.now();
    extensionNodesRegistry.registerAllNodes();
    const endTime = Date.now();
    
    // 获取注册统计
    const stats = extensionNodesRegistry.getRegistrationStats();
    const registeredTypes = extensionNodesRegistry.getAllRegisteredNodeTypes();
    
    Debug.log('Test', '注册完成统计:', {
      totalNodes: stats.totalNodes,
      vrArInputNodes: stats.vrArInputNodes,
      advancedInputNodes: stats.advancedInputNodes,
      sensorInputNodes: stats.sensorInputNodes,
      paymentNodes: stats.paymentNodes,
      integrationNodes: stats.integrationNodes,
      registrationTime: `${endTime - startTime}ms`,
      isComplete: stats.isComplete
    });
    
    // 验证节点数量
    if (stats.totalNodes === 30) {
      Debug.log('Test', '✅ 节点数量验证通过: 30个节点');
    } else {
      Debug.error('Test', `❌ 节点数量验证失败: 期望30个，实际${stats.totalNodes}个`);
    }
    
    // 验证各分类节点数量
    const expectedCounts = {
      vrArInputNodes: 8,
      advancedInputNodes: 4,
      sensorInputNodes: 6,
      paymentNodes: 6,
      integrationNodes: 6
    };
    
    let allCountsCorrect = true;
    Object.entries(expectedCounts).forEach(([category, expected]) => {
      const actual = stats[category];
      if (actual === expected) {
        Debug.log('Test', `✅ ${category}数量正确: ${actual}个`);
      } else {
        Debug.error('Test', `❌ ${category}数量错误: 期望${expected}个，实际${actual}个`);
        allCountsCorrect = false;
      }
    });
    
    // 验证注册状态
    if (extensionNodesRegistry.isRegistered()) {
      Debug.log('Test', '✅ 注册状态验证通过');
    } else {
      Debug.error('Test', '❌ 注册状态验证失败');
    }
    
    // 显示已注册的节点类型
    Debug.log('Test', '已注册的节点类型:', registeredTypes);
    
    // 验证关键节点类型
    const keyNodeTypes = [
      'input/vr_controller_input',
      'input/ar_touch_input',
      'input/multitouch_gesture',
      'input/accelerometer',
      'payment/gateway',
      'integration/google_services'
    ];
    
    let allKeyNodesRegistered = true;
    keyNodeTypes.forEach(nodeType => {
      if (registeredTypes.includes(nodeType)) {
        Debug.log('Test', `✅ 关键节点已注册: ${nodeType}`);
      } else {
        Debug.error('Test', `❌ 关键节点未注册: ${nodeType}`);
        allKeyNodesRegistered = false;
      }
    });
    
    // 测试重复注册
    Debug.log('Test', '测试重复注册...');
    extensionNodesRegistry.registerAllNodes();
    const statsAfterDuplicate = extensionNodesRegistry.getRegistrationStats();
    
    if (statsAfterDuplicate.totalNodes === stats.totalNodes) {
      Debug.log('Test', '✅ 重复注册防护正常');
    } else {
      Debug.error('Test', '❌ 重复注册防护失败');
    }
    
    // 总结测试结果
    const testsPassed = stats.totalNodes === 30 && 
                       allCountsCorrect && 
                       extensionNodesRegistry.isRegistered() && 
                       allKeyNodesRegistered;
    
    if (testsPassed) {
      Debug.log('Test', '🎉 批次R3扩展功能节点注册测试全部通过！');
    } else {
      Debug.error('Test', '❌ 批次R3扩展功能节点注册测试存在失败项');
    }
    
    return testsPassed;
    
  } catch (error) {
    Debug.error('Test', '批次R3节点注册测试失败:', error);
    return false;
  }
}

/**
 * 测试节点分类功能
 */
export function testNodeCategories(): void {
  Debug.log('Test', '=== 测试节点分类功能 ===');
  
  const registeredTypes = extensionNodesRegistry.getAllRegisteredNodeTypes();
  
  // 按分类统计节点
  const categoryStats = {
    vrArInput: registeredTypes.filter(type => type.startsWith('input/vr_') || type.startsWith('input/ar_') || type.includes('spatial') || type.includes('eye_tracking') || type.includes('hand_tracking') || type.includes('voice_command')).length,
    advancedInput: registeredTypes.filter(type => type.includes('multitouch') || type.includes('pressure') || type.includes('tilt') || type.includes('proximity')).length,
    sensorInput: registeredTypes.filter(type => type.includes('accelerometer') || type.includes('gyroscope') || type.includes('compass') || type.includes('light_sensor') || type.includes('pressure_sensor')).length,
    payment: registeredTypes.filter(type => type.startsWith('payment/')).length,
    integration: registeredTypes.filter(type => type.startsWith('integration/')).length
  };
  
  Debug.log('Test', '节点分类统计:', categoryStats);
  
  // 验证分类统计
  const expectedStats = {
    vrArInput: 8,
    advancedInput: 4,
    sensorInput: 6,
    payment: 6,
    integration: 6
  };
  
  let allCategoriesCorrect = true;
  Object.entries(expectedStats).forEach(([category, expected]) => {
    const actual = categoryStats[category];
    if (actual >= expected) {
      Debug.log('Test', `✅ ${category}分类节点数量正确: ${actual}个`);
    } else {
      Debug.error('Test', `❌ ${category}分类节点数量不足: 期望至少${expected}个，实际${actual}个`);
      allCategoriesCorrect = false;
    }
  });
  
  return allCategoriesCorrect;
}

/**
 * 运行所有测试
 */
export function runAllR3Tests(): boolean {
  Debug.log('Test', '🚀 开始运行批次R3扩展功能节点注册全部测试');
  
  const test1 = testR3NodesRegistration();
  const test2 = testNodeCategories();
  
  const allTestsPassed = test1 && test2;
  
  if (allTestsPassed) {
    Debug.log('Test', '🎉 所有批次R3测试通过！');
  } else {
    Debug.error('Test', '❌ 部分批次R3测试失败');
  }
  
  return allTestsPassed;
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllR3Tests();
}
