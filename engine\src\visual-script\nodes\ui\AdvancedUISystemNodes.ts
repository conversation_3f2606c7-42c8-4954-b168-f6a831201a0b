/**
 * 高级UI系统节点集合
 * 提供UI布局、UI动画和UI事件功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * UI布局类型枚举
 */
export enum UILayoutType {
  FLEX = 'flex',
  GRID = 'grid',
  ABSOLUTE = 'absolute',
  RELATIVE = 'relative',
  STACK = 'stack'
}

/**
 * UI动画类型枚举
 */
export enum UIAnimationType {
  FADE = 'fade',
  SLIDE = 'slide',
  SCALE = 'scale',
  ROTATE = 'rotate',
  BOUNCE = 'bounce',
  ELASTIC = 'elastic'
}

/**
 * UI事件类型枚举
 */
export enum UIEventType {
  CLICK = 'click',
  HOVER = 'hover',
  FOCUS = 'focus',
  BLUR = 'blur',
  DRAG = 'drag',
  DROP = 'drop',
  RESIZE = 'resize',
  SCROLL = 'scroll'
}

/**
 * UI布局节点
 * 管理UI元素的布局和排列
 */
export class UILayoutNode extends VisualScriptNode {
  public static TYPE = 'UILayout';
  public static NAME = 'UI布局';
  public static DESCRIPTION = '管理UI元素的布局和排列';

  private layoutConfig: any = {};
  private elements: Map<string, any> = new Map();
  private isActive: boolean = false;

  constructor() {
    super();
    this.title = UILayoutNode.NAME;
    this.category = 'ui';
    
    // 输入端口
    this.addInput('enable', '启用', 'boolean');
    this.addInput('layoutType', '布局类型', 'string');
    this.addInput('container', '容器', 'object');
    this.addInput('elements', '元素列表', 'array');
    this.addInput('spacing', '间距', 'number');
    this.addInput('padding', '内边距', 'object');
    this.addInput('alignment', '对齐方式', 'string');
    
    // 输出端口
    this.addOutput('isActive', '是否激活', 'boolean');
    this.addOutput('layoutType', '布局类型', 'string');
    this.addOutput('elementPositions', '元素位置', 'array');
    this.addOutput('containerSize', '容器大小', 'object');
    this.addOutput('onLayoutUpdated', '布局更新', 'trigger');
    this.addOutput('onElementAdded', '元素添加', 'trigger');
    this.addOutput('onElementRemoved', '元素移除', 'trigger');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable as boolean ?? true;
      const layoutType = inputs?.layoutType as string ?? 'flex';
      const container = inputs?.container as any;
      const elements = inputs?.elements as any[] ?? [];
      const spacing = inputs?.spacing as number ?? 10;
      const padding = inputs?.padding as any ?? { top: 0, right: 0, bottom: 0, left: 0 };
      const alignment = inputs?.alignment as string ?? 'start';

      if (enable && !this.isActive) {
        this.enableLayout();
      } else if (!enable && this.isActive) {
        this.disableLayout();
      }

      if (this.isActive) {
        this.updateLayoutConfig({
          type: layoutType,
          container,
          spacing,
          padding,
          alignment
        });

        this.updateElements(elements);
        this.calculateLayout();
      }

      return {
        isActive: this.isActive,
        layoutType: this.layoutConfig.type || layoutType,
        elementPositions: this.getElementPositions(),
        containerSize: this.getContainerSize(),
        onLayoutUpdated: this.isActive,
        onElementAdded: false,
        onElementRemoved: false
      };

    } catch (error) {
      Debug.error('UILayoutNode', 'UI布局处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private enableLayout(): void {
    this.isActive = true;
    Debug.log('UILayoutNode', 'UI布局已启用');
  }

  private disableLayout(): void {
    this.isActive = false;
    this.elements.clear();
    Debug.log('UILayoutNode', 'UI布局已禁用');
  }

  private updateLayoutConfig(config: any): void {
    this.layoutConfig = { ...this.layoutConfig, ...config };
    Debug.log('UILayoutNode', '布局配置已更新', this.layoutConfig);
  }

  private updateElements(elements: any[]): void {
    this.elements.clear();
    elements.forEach((element, index) => {
      this.elements.set(element.id || `element_${index}`, element);
    });
  }

  private calculateLayout(): void {
    const layoutType = this.layoutConfig.type;
    const spacing = this.layoutConfig.spacing || 10;
    const padding = this.layoutConfig.padding || { top: 0, right: 0, bottom: 0, left: 0 };

    switch (layoutType) {
      case UILayoutType.FLEX:
        this.calculateFlexLayout(spacing, padding);
        break;
      case UILayoutType.GRID:
        this.calculateGridLayout(spacing, padding);
        break;
      case UILayoutType.ABSOLUTE:
        this.calculateAbsoluteLayout();
        break;
      case UILayoutType.STACK:
        this.calculateStackLayout(spacing, padding);
        break;
      default:
        this.calculateFlexLayout(spacing, padding);
    }
  }

  private calculateFlexLayout(spacing: number, padding: any): void {
    let currentX = padding.left;
    let currentY = padding.top;

    for (const [id, element] of this.elements) {
      element.calculatedPosition = { x: currentX, y: currentY };
      currentX += (element.width || 100) + spacing;
    }

    Debug.log('UILayoutNode', 'Flex布局计算完成');
  }

  private calculateGridLayout(spacing: number, padding: any): void {
    const columns = this.layoutConfig.columns || 3;
    let currentColumn = 0;
    let currentRow = 0;

    for (const [id, element] of this.elements) {
      const x = padding.left + currentColumn * ((element.width || 100) + spacing);
      const y = padding.top + currentRow * ((element.height || 100) + spacing);
      
      element.calculatedPosition = { x, y };
      
      currentColumn++;
      if (currentColumn >= columns) {
        currentColumn = 0;
        currentRow++;
      }
    }

    Debug.log('UILayoutNode', 'Grid布局计算完成');
  }

  private calculateAbsoluteLayout(): void {
    for (const [id, element] of this.elements) {
      element.calculatedPosition = element.position || { x: 0, y: 0 };
    }

    Debug.log('UILayoutNode', 'Absolute布局计算完成');
  }

  private calculateStackLayout(spacing: number, padding: any): void {
    let currentY = padding.top;

    for (const [id, element] of this.elements) {
      element.calculatedPosition = { x: padding.left, y: currentY };
      currentY += (element.height || 100) + spacing;
    }

    Debug.log('UILayoutNode', 'Stack布局计算完成');
  }

  private getElementPositions(): any[] {
    return Array.from(this.elements.entries()).map(([id, element]) => ({
      id,
      position: element.calculatedPosition || { x: 0, y: 0 },
      size: { width: element.width || 100, height: element.height || 100 }
    }));
  }

  private getContainerSize(): any {
    if (!this.layoutConfig.container) {
      return { width: 800, height: 600 };
    }
    return {
      width: this.layoutConfig.container.width || 800,
      height: this.layoutConfig.container.height || 600
    };
  }

  private getDefaultOutputs(): any {
    return {
      isActive: false,
      layoutType: 'flex',
      elementPositions: [],
      containerSize: { width: 800, height: 600 },
      onLayoutUpdated: false,
      onElementAdded: false,
      onElementRemoved: false
    };
  }
}

/**
 * UI动画节点
 * 创建和控制UI元素动画效果
 */
export class UIAnimationNode extends VisualScriptNode {
  public static TYPE = 'UIAnimation';
  public static NAME = 'UI动画';
  public static DESCRIPTION = '创建和控制UI元素动画效果';

  private animations: Map<string, any> = new Map();
  private isActive: boolean = false;

  constructor() {
    super();
    this.title = UIAnimationNode.NAME;
    this.category = 'ui';
    
    // 输入端口
    this.addInput('enable', '启用', 'boolean');
    this.addInput('target', '目标元素', 'object');
    this.addInput('animationType', '动画类型', 'string');
    this.addInput('duration', '持续时间', 'number');
    this.addInput('easing', '缓动函数', 'string');
    this.addInput('fromValue', '起始值', 'any');
    this.addInput('toValue', '结束值', 'any');
    this.addInput('play', '播放', 'trigger');
    this.addInput('pause', '暂停', 'trigger');
    this.addInput('stop', '停止', 'trigger');
    
    // 输出端口
    this.addOutput('isActive', '是否激活', 'boolean');
    this.addOutput('isPlaying', '是否播放', 'boolean');
    this.addOutput('progress', '进度', 'number');
    this.addOutput('currentValue', '当前值', 'any');
    this.addOutput('onStart', '动画开始', 'trigger');
    this.addOutput('onUpdate', '动画更新', 'trigger');
    this.addOutput('onComplete', '动画完成', 'trigger');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable as boolean ?? true;
      const target = inputs?.target as any;
      const animationType = inputs?.animationType as string ?? 'fade';
      const duration = inputs?.duration as number ?? 1000;
      const easing = inputs?.easing as string ?? 'ease';
      const fromValue = inputs?.fromValue;
      const toValue = inputs?.toValue;
      const playTrigger = inputs?.play;
      const pauseTrigger = inputs?.pause;
      const stopTrigger = inputs?.stop;

      if (enable && !this.isActive) {
        this.enableAnimation();
      } else if (!enable && this.isActive) {
        this.disableAnimation();
      }

      if (target && this.isActive) {
        const animationId = target.id || 'default';
        
        if (playTrigger) {
          return this.playAnimation(animationId, {
            target,
            type: animationType,
            duration,
            easing,
            fromValue,
            toValue
          });
        } else if (pauseTrigger) {
          return this.pauseAnimation(animationId);
        } else if (stopTrigger) {
          return this.stopAnimation(animationId);
        }
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('UIAnimationNode', 'UI动画处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private enableAnimation(): void {
    this.isActive = true;
    Debug.log('UIAnimationNode', 'UI动画已启用');
  }

  private disableAnimation(): void {
    this.isActive = false;
    this.animations.clear();
    Debug.log('UIAnimationNode', 'UI动画已禁用');
  }

  private playAnimation(id: string, config: any): any {
    const animation = {
      id,
      config,
      startTime: Date.now(),
      isPlaying: true,
      progress: 0
    };

    this.animations.set(id, animation);
    Debug.log('UIAnimationNode', `播放动画: ${id}`, config);

    return {
      isActive: this.isActive,
      isPlaying: true,
      progress: 0,
      currentValue: config.fromValue,
      onStart: true,
      onUpdate: false,
      onComplete: false
    };
  }

  private pauseAnimation(id: string): any {
    const animation = this.animations.get(id);
    if (animation) {
      animation.isPlaying = false;
      Debug.log('UIAnimationNode', `暂停动画: ${id}`);
    }

    return {
      isActive: this.isActive,
      isPlaying: false,
      progress: animation?.progress || 0,
      currentValue: null,
      onStart: false,
      onUpdate: false,
      onComplete: false
    };
  }

  private stopAnimation(id: string): any {
    this.animations.delete(id);
    Debug.log('UIAnimationNode', `停止动画: ${id}`);

    return {
      isActive: this.isActive,
      isPlaying: false,
      progress: 0,
      currentValue: null,
      onStart: false,
      onUpdate: false,
      onComplete: true
    };
  }

  private getDefaultOutputs(): any {
    return {
      isActive: false,
      isPlaying: false,
      progress: 0,
      currentValue: null,
      onStart: false,
      onUpdate: false,
      onComplete: false
    };
  }
}

/**
 * UI事件节点
 * 处理UI交互事件和响应
 */
export class UIEventNode extends VisualScriptNode {
  public static TYPE = 'UIEvent';
  public static NAME = 'UI事件';
  public static DESCRIPTION = '处理UI交互事件和响应';

  private eventListeners: Map<string, Function[]> = new Map();
  private targetElements: Map<string, any> = new Map();
  private isActive: boolean = false;

  constructor() {
    super();
    this.title = UIEventNode.NAME;
    this.category = 'ui';

    // 输入端口
    this.addInput('enable', '启用', 'boolean');
    this.addInput('target', '目标元素', 'object');
    this.addInput('eventType', '事件类型', 'string');
    this.addInput('eventData', '事件数据', 'any');
    this.addInput('preventDefault', '阻止默认', 'boolean');
    this.addInput('stopPropagation', '停止传播', 'boolean');

    // 输出端口
    this.addOutput('isActive', '是否激活', 'boolean');
    this.addOutput('eventType', '事件类型', 'string');
    this.addOutput('eventData', '事件数据', 'any');
    this.addOutput('targetElement', '目标元素', 'object');
    this.addOutput('mousePosition', '鼠标位置', 'object');
    this.addOutput('keyCode', '按键代码', 'string');
    this.addOutput('onClick', '点击事件', 'trigger');
    this.addOutput('onHover', '悬停事件', 'trigger');
    this.addOutput('onFocus', '焦点事件', 'trigger');
    this.addOutput('onBlur', '失焦事件', 'trigger');
    this.addOutput('onDrag', '拖拽事件', 'trigger');
    this.addOutput('onDrop', '放置事件', 'trigger');
    this.addOutput('onResize', '调整大小事件', 'trigger');
    this.addOutput('onScroll', '滚动事件', 'trigger');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable as boolean ?? true;
      const target = inputs?.target as any;
      const eventType = inputs?.eventType as string;
      const eventData = inputs?.eventData;
      const preventDefault = inputs?.preventDefault as boolean ?? false;
      const stopPropagation = inputs?.stopPropagation as boolean ?? false;

      if (enable && !this.isActive) {
        this.enableEventHandling();
      } else if (!enable && this.isActive) {
        this.disableEventHandling();
      }

      if (target && eventType && this.isActive) {
        return this.handleEvent(target, eventType, eventData, preventDefault, stopPropagation);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('UIEventNode', 'UI事件处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private enableEventHandling(): void {
    this.isActive = true;
    Debug.log('UIEventNode', 'UI事件处理已启用');
  }

  private disableEventHandling(): void {
    this.isActive = false;
    this.eventListeners.clear();
    this.targetElements.clear();
    Debug.log('UIEventNode', 'UI事件处理已禁用');
  }

  private handleEvent(target: any, eventType: string, eventData: any, preventDefault: boolean, stopPropagation: boolean): any {
    const targetId = target.id || 'default';
    this.targetElements.set(targetId, target);

    // 注册事件监听器
    this.registerEventListener(targetId, eventType);

    // 模拟事件处理
    const processedEventData = this.processEventData(eventType, eventData);

    Debug.log('UIEventNode', `处理UI事件: ${eventType}`, processedEventData);

    // 根据事件类型返回相应的输出
    const outputs = this.getDefaultOutputs();
    outputs.isActive = true;
    outputs.eventType = eventType;
    outputs.eventData = processedEventData;
    outputs.targetElement = target;

    // 设置特定事件的触发器
    switch (eventType) {
      case UIEventType.CLICK:
        outputs.onClick = true;
        outputs.mousePosition = processedEventData.mousePosition || { x: 0, y: 0 };
        break;
      case UIEventType.HOVER:
        outputs.onHover = true;
        outputs.mousePosition = processedEventData.mousePosition || { x: 0, y: 0 };
        break;
      case UIEventType.FOCUS:
        outputs.onFocus = true;
        break;
      case UIEventType.BLUR:
        outputs.onBlur = true;
        break;
      case UIEventType.DRAG:
        outputs.onDrag = true;
        outputs.mousePosition = processedEventData.mousePosition || { x: 0, y: 0 };
        break;
      case UIEventType.DROP:
        outputs.onDrop = true;
        outputs.mousePosition = processedEventData.mousePosition || { x: 0, y: 0 };
        break;
      case UIEventType.RESIZE:
        outputs.onResize = true;
        break;
      case UIEventType.SCROLL:
        outputs.onScroll = true;
        break;
    }

    return outputs;
  }

  private registerEventListener(targetId: string, eventType: string): void {
    const key = `${targetId}_${eventType}`;
    if (!this.eventListeners.has(key)) {
      const listener = (event: any) => {
        Debug.log('UIEventNode', `事件触发: ${eventType}`, event);
      };
      this.eventListeners.set(key, [listener]);
    }
  }

  private processEventData(eventType: string, eventData: any): any {
    const processed = { ...eventData };

    // 根据事件类型处理特定数据
    switch (eventType) {
      case UIEventType.CLICK:
      case UIEventType.HOVER:
      case UIEventType.DRAG:
      case UIEventType.DROP:
        if (!processed.mousePosition) {
          processed.mousePosition = { x: 0, y: 0 };
        }
        break;
      case UIEventType.RESIZE:
        if (!processed.size) {
          processed.size = { width: 100, height: 100 };
        }
        break;
      case UIEventType.SCROLL:
        if (!processed.scrollPosition) {
          processed.scrollPosition = { x: 0, y: 0 };
        }
        break;
    }

    return processed;
  }

  private getDefaultOutputs(): any {
    return {
      isActive: false,
      eventType: '',
      eventData: null,
      targetElement: null,
      mousePosition: { x: 0, y: 0 },
      keyCode: '',
      onClick: false,
      onHover: false,
      onFocus: false,
      onBlur: false,
      onDrag: false,
      onDrop: false,
      onResize: false,
      onScroll: false
    };
  }
}
