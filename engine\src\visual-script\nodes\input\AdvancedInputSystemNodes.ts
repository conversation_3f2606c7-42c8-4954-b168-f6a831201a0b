/**
 * 高级输入系统节点集合
 * 提供触摸、手柄、键盘、鼠标和自定义输入功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 输入设备状态枚举
 */
export enum InputDeviceState {
  DISCONNECTED = 'disconnected',
  CONNECTED = 'connected',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error'
}

/**
 * 触摸手势类型枚举
 */
export enum TouchGestureType {
  TAP = 'tap',
  DOUBLE_TAP = 'double_tap',
  LONG_PRESS = 'long_press',
  SWIPE = 'swipe',
  PINCH = 'pinch',
  ROTATE = 'rotate',
  PAN = 'pan'
}

/**
 * 手柄按钮枚举
 */
export enum GamepadButton {
  A = 0,
  B = 1,
  X = 2,
  Y = 3,
  LEFT_BUMPER = 4,
  RIGHT_BUMPER = 5,
  LEFT_TRIGGER = 6,
  RIGHT_TRIGGER = 7,
  SELECT = 8,
  START = 9,
  LEFT_STICK = 10,
  RIGHT_STICK = 11,
  DPAD_UP = 12,
  DPAD_DOWN = 13,
  DPAD_LEFT = 14,
  DPAD_RIGHT = 15
}

/**
 * 触摸输入节点
 * 处理触摸屏输入和手势识别
 */
export class TouchInputNode extends VisualScriptNode {
  public static TYPE = 'TouchInput';
  public static NAME = '触摸输入';
  public static DESCRIPTION = '处理触摸屏输入和手势识别';

  private touchState: Map<number, any> = new Map();
  private gestureRecognizer: any = null;
  private isActive: boolean = false;

  constructor() {
    super();
    this.title = TouchInputNode.NAME;
    this.category = 'input';
    
    // 输入端口
    this.addInput('enable', '启用', 'boolean');
    this.addInput('sensitivity', '灵敏度', 'number');
    this.addInput('gestureTypes', '手势类型', 'array');
    
    // 输出端口
    this.addOutput('isActive', '是否激活', 'boolean');
    this.addOutput('touchCount', '触摸点数', 'number');
    this.addOutput('touchPositions', '触摸位置', 'array');
    this.addOutput('gestureType', '手势类型', 'string');
    this.addOutput('gestureData', '手势数据', 'object');
    this.addOutput('onTouchStart', '触摸开始', 'trigger');
    this.addOutput('onTouchMove', '触摸移动', 'trigger');
    this.addOutput('onTouchEnd', '触摸结束', 'trigger');
    this.addOutput('onGesture', '手势识别', 'trigger');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable as boolean ?? true;
      const sensitivity = inputs?.sensitivity as number ?? 1.0;
      const gestureTypes = inputs?.gestureTypes as string[] ?? ['tap', 'swipe'];

      if (enable && !this.isActive) {
        this.enableTouchInput(sensitivity, gestureTypes);
      } else if (!enable && this.isActive) {
        this.disableTouchInput();
      }

      return {
        isActive: this.isActive,
        touchCount: this.touchState.size,
        touchPositions: this.getTouchPositions(),
        gestureType: '',
        gestureData: {},
        onTouchStart: false,
        onTouchMove: false,
        onTouchEnd: false,
        onGesture: false
      };

    } catch (error) {
      Debug.error('TouchInputNode', '触摸输入处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private enableTouchInput(sensitivity: number, gestureTypes: string[]): void {
    this.isActive = true;
    this.setupTouchEventListeners();
    this.initializeGestureRecognizer(gestureTypes);
    Debug.log('TouchInputNode', `触摸输入已启用，灵敏度: ${sensitivity}`);
  }

  private disableTouchInput(): void {
    this.isActive = false;
    this.removeTouchEventListeners();
    this.touchState.clear();
    Debug.log('TouchInputNode', '触摸输入已禁用');
  }

  private setupTouchEventListeners(): void {
    // 模拟触摸事件监听器设置
    Debug.log('TouchInputNode', '设置触摸事件监听器');
  }

  private removeTouchEventListeners(): void {
    // 模拟触摸事件监听器移除
    Debug.log('TouchInputNode', '移除触摸事件监听器');
  }

  private initializeGestureRecognizer(gestureTypes: string[]): void {
    this.gestureRecognizer = {
      types: gestureTypes,
      sensitivity: 1.0
    };
    Debug.log('TouchInputNode', '手势识别器已初始化', gestureTypes);
  }

  private getTouchPositions(): any[] {
    return Array.from(this.touchState.values()).map(touch => ({
      id: touch.id,
      x: touch.x,
      y: touch.y
    }));
  }

  private getDefaultOutputs(): any {
    return {
      isActive: false,
      touchCount: 0,
      touchPositions: [],
      gestureType: '',
      gestureData: {},
      onTouchStart: false,
      onTouchMove: false,
      onTouchEnd: false,
      onGesture: false
    };
  }
}

/**
 * 手柄输入节点
 * 处理游戏手柄输入和控制
 */
export class GamepadInputNode extends VisualScriptNode {
  public static TYPE = 'GamepadInput';
  public static NAME = '手柄输入';
  public static DESCRIPTION = '处理游戏手柄输入和控制';

  private gamepadState: any = null;
  private isActive: boolean = false;
  private gamepadIndex: number = 0;

  constructor() {
    super();
    this.title = GamepadInputNode.NAME;
    this.category = 'input';
    
    // 输入端口
    this.addInput('enable', '启用', 'boolean');
    this.addInput('gamepadIndex', '手柄索引', 'number');
    this.addInput('deadzone', '死区', 'number');
    
    // 输出端口
    this.addOutput('isActive', '是否激活', 'boolean');
    this.addOutput('isConnected', '是否连接', 'boolean');
    this.addOutput('buttons', '按钮状态', 'array');
    this.addOutput('axes', '摇杆轴', 'array');
    this.addOutput('leftStick', '左摇杆', 'object');
    this.addOutput('rightStick', '右摇杆', 'object');
    this.addOutput('triggers', '扳机键', 'object');
    this.addOutput('onButtonPressed', '按钮按下', 'trigger');
    this.addOutput('onButtonReleased', '按钮释放', 'trigger');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable as boolean ?? true;
      const gamepadIndex = inputs?.gamepadIndex as number ?? 0;
      const deadzone = inputs?.deadzone as number ?? 0.1;

      this.gamepadIndex = gamepadIndex;

      if (enable && !this.isActive) {
        this.enableGamepadInput(deadzone);
      } else if (!enable && this.isActive) {
        this.disableGamepadInput();
      }

      if (this.isActive) {
        this.updateGamepadState();
      }

      return {
        isActive: this.isActive,
        isConnected: this.gamepadState !== null,
        buttons: this.getButtonStates(),
        axes: this.getAxesStates(),
        leftStick: this.getLeftStickState(),
        rightStick: this.getRightStickState(),
        triggers: this.getTriggerStates(),
        onButtonPressed: false,
        onButtonReleased: false
      };

    } catch (error) {
      Debug.error('GamepadInputNode', '手柄输入处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private enableGamepadInput(deadzone: number): void {
    this.isActive = true;
    this.updateGamepadState();
    Debug.log('GamepadInputNode', `手柄输入已启用，死区: ${deadzone}`);
  }

  private disableGamepadInput(): void {
    this.isActive = false;
    this.gamepadState = null;
    Debug.log('GamepadInputNode', '手柄输入已禁用');
  }

  private updateGamepadState(): void {
    if (typeof navigator !== 'undefined' && navigator.getGamepads) {
      const gamepads = navigator.getGamepads();
      this.gamepadState = gamepads[this.gamepadIndex] || null;
    }
  }

  private getButtonStates(): any[] {
    if (!this.gamepadState) return [];
    return this.gamepadState.buttons.map((button: any, index: number) => ({
      index,
      pressed: button.pressed,
      value: button.value
    }));
  }

  private getAxesStates(): number[] {
    if (!this.gamepadState) return [];
    return Array.from(this.gamepadState.axes);
  }

  private getLeftStickState(): any {
    if (!this.gamepadState) return { x: 0, y: 0 };
    return {
      x: this.gamepadState.axes[0] || 0,
      y: this.gamepadState.axes[1] || 0
    };
  }

  private getRightStickState(): any {
    if (!this.gamepadState) return { x: 0, y: 0 };
    return {
      x: this.gamepadState.axes[2] || 0,
      y: this.gamepadState.axes[3] || 0
    };
  }

  private getTriggerStates(): any {
    if (!this.gamepadState) return { left: 0, right: 0 };
    return {
      left: this.gamepadState.buttons[GamepadButton.LEFT_TRIGGER]?.value || 0,
      right: this.gamepadState.buttons[GamepadButton.RIGHT_TRIGGER]?.value || 0
    };
  }

  private getDefaultOutputs(): any {
    return {
      isActive: false,
      isConnected: false,
      buttons: [],
      axes: [],
      leftStick: { x: 0, y: 0 },
      rightStick: { x: 0, y: 0 },
      triggers: { left: 0, right: 0 },
      onButtonPressed: false,
      onButtonReleased: false
    };
  }
}

/**
 * 键盘输入节点
 * 处理键盘按键输入和快捷键
 */
export class KeyboardInputNode extends VisualScriptNode {
  public static TYPE = 'KeyboardInput';
  public static NAME = '键盘输入';
  public static DESCRIPTION = '处理键盘按键输入和快捷键';

  private keyStates: Map<string, boolean> = new Map();
  private shortcuts: Map<string, string[]> = new Map();
  private isActive: boolean = false;

  constructor() {
    super();
    this.title = KeyboardInputNode.NAME;
    this.category = 'input';

    // 输入端口
    this.addInput('enable', '启用', 'boolean');
    this.addInput('keyCode', '按键代码', 'string');
    this.addInput('shortcuts', '快捷键', 'object');

    // 输出端口
    this.addOutput('isActive', '是否激活', 'boolean');
    this.addOutput('pressedKeys', '按下的键', 'array');
    this.addOutput('keyPressed', '按键按下', 'string');
    this.addOutput('keyReleased', '按键释放', 'string');
    this.addOutput('shortcutTriggered', '快捷键触发', 'string');
    this.addOutput('onKeyDown', '按键按下', 'trigger');
    this.addOutput('onKeyUp', '按键释放', 'trigger');
    this.addOutput('onShortcut', '快捷键', 'trigger');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable as boolean ?? true;
      const keyCode = inputs?.keyCode as string;
      const shortcuts = inputs?.shortcuts as any;

      if (enable && !this.isActive) {
        this.enableKeyboardInput();
      } else if (!enable && this.isActive) {
        this.disableKeyboardInput();
      }

      if (shortcuts) {
        this.updateShortcuts(shortcuts);
      }

      if (keyCode) {
        return this.checkKeyState(keyCode);
      }

      return {
        isActive: this.isActive,
        pressedKeys: Array.from(this.keyStates.entries())
          .filter(([_, pressed]) => pressed)
          .map(([key, _]) => key),
        keyPressed: '',
        keyReleased: '',
        shortcutTriggered: '',
        onKeyDown: false,
        onKeyUp: false,
        onShortcut: false
      };

    } catch (error) {
      Debug.error('KeyboardInputNode', '键盘输入处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private enableKeyboardInput(): void {
    this.isActive = true;
    this.setupKeyboardEventListeners();
    Debug.log('KeyboardInputNode', '键盘输入已启用');
  }

  private disableKeyboardInput(): void {
    this.isActive = false;
    this.removeKeyboardEventListeners();
    this.keyStates.clear();
    Debug.log('KeyboardInputNode', '键盘输入已禁用');
  }

  private setupKeyboardEventListeners(): void {
    // 模拟键盘事件监听器设置
    Debug.log('KeyboardInputNode', '设置键盘事件监听器');
  }

  private removeKeyboardEventListeners(): void {
    // 模拟键盘事件监听器移除
    Debug.log('KeyboardInputNode', '移除键盘事件监听器');
  }

  private updateShortcuts(shortcuts: any): void {
    this.shortcuts.clear();
    for (const [name, keys] of Object.entries(shortcuts)) {
      this.shortcuts.set(name, keys as string[]);
    }
    Debug.log('KeyboardInputNode', '快捷键已更新', shortcuts);
  }

  private checkKeyState(keyCode: string): any {
    const isPressed = this.keyStates.get(keyCode) || false;
    return {
      isActive: this.isActive,
      pressedKeys: Array.from(this.keyStates.entries())
        .filter(([_, pressed]) => pressed)
        .map(([key, _]) => key),
      keyPressed: isPressed ? keyCode : '',
      keyReleased: '',
      shortcutTriggered: '',
      onKeyDown: isPressed,
      onKeyUp: false,
      onShortcut: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      isActive: false,
      pressedKeys: [],
      keyPressed: '',
      keyReleased: '',
      shortcutTriggered: '',
      onKeyDown: false,
      onKeyUp: false,
      onShortcut: false
    };
  }
}

/**
 * 鼠标输入节点
 * 处理鼠标点击、移动和滚轮输入
 */
export class MouseInputNode extends VisualScriptNode {
  public static TYPE = 'MouseInput';
  public static NAME = '鼠标输入';
  public static DESCRIPTION = '处理鼠标点击、移动和滚轮输入';

  private mouseState: any = {
    position: { x: 0, y: 0 },
    buttons: { left: false, right: false, middle: false },
    wheel: { deltaX: 0, deltaY: 0 }
  };
  private isActive: boolean = false;

  constructor() {
    super();
    this.title = MouseInputNode.NAME;
    this.category = 'input';

    // 输入端口
    this.addInput('enable', '启用', 'boolean');
    this.addInput('sensitivity', '灵敏度', 'number');

    // 输出端口
    this.addOutput('isActive', '是否激活', 'boolean');
    this.addOutput('position', '鼠标位置', 'object');
    this.addOutput('deltaPosition', '位置变化', 'object');
    this.addOutput('leftButton', '左键', 'boolean');
    this.addOutput('rightButton', '右键', 'boolean');
    this.addOutput('middleButton', '中键', 'boolean');
    this.addOutput('wheelDelta', '滚轮变化', 'object');
    this.addOutput('onMove', '鼠标移动', 'trigger');
    this.addOutput('onClick', '鼠标点击', 'trigger');
    this.addOutput('onWheel', '滚轮滚动', 'trigger');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable as boolean ?? true;
      const sensitivity = inputs?.sensitivity as number ?? 1.0;

      if (enable && !this.isActive) {
        this.enableMouseInput(sensitivity);
      } else if (!enable && this.isActive) {
        this.disableMouseInput();
      }

      return {
        isActive: this.isActive,
        position: this.mouseState.position,
        deltaPosition: { x: 0, y: 0 },
        leftButton: this.mouseState.buttons.left,
        rightButton: this.mouseState.buttons.right,
        middleButton: this.mouseState.buttons.middle,
        wheelDelta: this.mouseState.wheel,
        onMove: false,
        onClick: false,
        onWheel: false
      };

    } catch (error) {
      Debug.error('MouseInputNode', '鼠标输入处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private enableMouseInput(sensitivity: number): void {
    this.isActive = true;
    this.setupMouseEventListeners();
    Debug.log('MouseInputNode', `鼠标输入已启用，灵敏度: ${sensitivity}`);
  }

  private disableMouseInput(): void {
    this.isActive = false;
    this.removeMouseEventListeners();
    Debug.log('MouseInputNode', '鼠标输入已禁用');
  }

  private setupMouseEventListeners(): void {
    // 模拟鼠标事件监听器设置
    Debug.log('MouseInputNode', '设置鼠标事件监听器');
  }

  private removeMouseEventListeners(): void {
    // 模拟鼠标事件监听器移除
    Debug.log('MouseInputNode', '移除鼠标事件监听器');
  }

  private getDefaultOutputs(): any {
    return {
      isActive: false,
      position: { x: 0, y: 0 },
      deltaPosition: { x: 0, y: 0 },
      leftButton: false,
      rightButton: false,
      middleButton: false,
      wheelDelta: { deltaX: 0, deltaY: 0 },
      onMove: false,
      onClick: false,
      onWheel: false
    };
  }
}

/**
 * 自定义输入节点
 * 创建和处理自定义输入设备
 */
export class CustomInputNode extends VisualScriptNode {
  public static TYPE = 'CustomInput';
  public static NAME = '自定义输入';
  public static DESCRIPTION = '创建和处理自定义输入设备';

  private customInputs: Map<string, any> = new Map();
  private inputHandlers: Map<string, Function> = new Map();
  private isActive: boolean = false;

  constructor() {
    super();
    this.title = CustomInputNode.NAME;
    this.category = 'input';

    // 输入端口
    this.addInput('enable', '启用', 'boolean');
    this.addInput('inputName', '输入名称', 'string');
    this.addInput('inputValue', '输入值', 'any');
    this.addInput('inputConfig', '输入配置', 'object');

    // 输出端口
    this.addOutput('isActive', '是否激活', 'boolean');
    this.addOutput('inputNames', '输入名称列表', 'array');
    this.addOutput('inputValues', '输入值', 'object');
    this.addOutput('triggeredInput', '触发的输入', 'string');
    this.addOutput('onInputChanged', '输入变化', 'trigger');
    this.addOutput('onInputAdded', '输入添加', 'trigger');
    this.addOutput('onInputRemoved', '输入移除', 'trigger');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable as boolean ?? true;
      const inputName = inputs?.inputName as string;
      const inputValue = inputs?.inputValue;
      const inputConfig = inputs?.inputConfig as any;

      if (enable && !this.isActive) {
        this.enableCustomInput();
      } else if (!enable && this.isActive) {
        this.disableCustomInput();
      }

      if (inputName && inputValue !== undefined) {
        this.setCustomInput(inputName, inputValue);
      }

      if (inputConfig) {
        this.configureCustomInput(inputConfig);
      }

      return {
        isActive: this.isActive,
        inputNames: Array.from(this.customInputs.keys()),
        inputValues: Object.fromEntries(this.customInputs),
        triggeredInput: inputName || '',
        onInputChanged: inputName !== undefined,
        onInputAdded: false,
        onInputRemoved: false
      };

    } catch (error) {
      Debug.error('CustomInputNode', '自定义输入处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private enableCustomInput(): void {
    this.isActive = true;
    Debug.log('CustomInputNode', '自定义输入已启用');
  }

  private disableCustomInput(): void {
    this.isActive = false;
    this.customInputs.clear();
    this.inputHandlers.clear();
    Debug.log('CustomInputNode', '自定义输入已禁用');
  }

  private setCustomInput(name: string, value: any): void {
    this.customInputs.set(name, value);
    Debug.log('CustomInputNode', `设置自定义输入: ${name} = ${value}`);
  }

  private configureCustomInput(config: any): void {
    for (const [name, settings] of Object.entries(config)) {
      this.customInputs.set(name, settings);
    }
    Debug.log('CustomInputNode', '自定义输入配置已更新', config);
  }

  private getDefaultOutputs(): any {
    return {
      isActive: false,
      inputNames: [],
      inputValues: {},
      triggeredInput: '',
      onInputChanged: false,
      onInputAdded: false,
      onInputRemoved: false
    };
  }
}
