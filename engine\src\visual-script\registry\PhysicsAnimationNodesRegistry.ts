/**
 * 注册批次R1：物理与动画系统节点注册表（已完成）
 * 注册物理系统节点（17个）、动画系统节点（11个）和音频系统节点（12个）到编辑器
 * 总计40个节点
 */

import { NodeRegistry } from './NodeRegistry';

// 导入物理系统节点
import {
  SoftBodyPhysicsNode,
  FluidSimulationNode,
  ClothSimulationNode,
  RopeSimulationNode,
  DestructionNode,
  PhysicsConstraintNode
} from '../nodes/physics/AdvancedPhysicsNodes';

import { ClothSystemNode } from '../nodes/physics/SoftBodyNodes';

import {
  PhysicsOptimizationNode,
  PhysicsLODNode,
  PhysicsPerformanceMonitorNode,
  PhysicsBatchingNode
} from '../nodes/physics/PhysicsOptimizationNodes';

// 导入动画系统节点
import {
  TweenNode,
  KeyframeAnimationNode,
  AnimationBlendTreeNode,
  AnimationStateMachineNode,
  IKSystemNode,
  AnimationRetargetingNode,
  AnimationCompressionNode,
  AnimationOptimizationNode
} from '../nodes/animation/AnimationNodes';

import {
  AnimationStateMachineNode as AdvancedAnimationStateMachineNode,
  AnimationBlendNode,
  IKSystemNode as AdvancedIKSystemNode,
  AnimationEventNode
} from '../nodes/animation/AdvancedAnimationNodes';

import {
  AnimationBakingNode,
  AnimationExportNode,
  AnimationImportNode,
  AnimationValidationNode
} from '../nodes/animation/AnimationToolNodes';

// 导入音频系统节点（12个）
import {
  SpatialAudioNode,
  AudioFilterNode,
  AudioEffectNode,
  AudioMixerNode,
  AudioAnalyzerNode,
  AudioCompressionNode,
  AudioEqualizerNode,
  AudioReverbNode,
  EchoNode,
  AudioVisualizationNode,
  AudioRecorderNode,
  AudioStreamingNode
} from '../nodes/audio/AdvancedAudioSystemNodes';

/**
 * 物理与动画系统节点注册表
 */
export class PhysicsAnimationNodesRegistry {
  private nodeRegistry: NodeRegistry;
  private registered: boolean = false;

  constructor(nodeRegistry: NodeRegistry) {
    this.nodeRegistry = nodeRegistry;
  }

  /**
   * 注册所有物理与动画系统节点
   */
  public registerAll(): void {
    if (this.registered) {
      console.warn('物理与动画系统节点已经注册过了');
      return;
    }

    console.log('开始注册批次R1：物理与动画系统节点（40个）...');

    try {
      // 注册物理系统节点（17个）
      this.registerPhysicsNodes();

      // 注册动画系统节点（11个）
      this.registerAnimationNodes();

      // 注册音频系统节点（12个）
      this.registerAudioNodes();

      this.registered = true;
      console.log('✅ 批次R1物理与动画系统节点注册完成 - 40个节点');

      // 输出注册统计
      this.logRegistrationStats();

    } catch (error) {
      console.error('❌ 批次R1物理与动画系统节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册物理系统节点（17个）
   */
  private registerPhysicsNodes(): void {
    console.log('注册物理系统节点...');

    // 软体物理节点（4个）
    const softBodyNodes = [
      { 
        class: SoftBodyPhysicsNode, 
        type: 'SoftBodyPhysics', 
        name: '软体物理',
        desc: '创建和控制软体物理对象，支持变形和弹性模拟'
      },
      { 
        class: FluidSimulationNode, 
        type: 'FluidSimulation', 
        name: '流体模拟',
        desc: '创建和控制流体模拟系统，支持液体和气体模拟'
      },
      { 
        class: ClothSimulationNode, 
        type: 'ClothSimulation', 
        name: '布料模拟',
        desc: '创建和控制布料物理模拟，支持织物动态效果'
      },
      { 
        class: ClothSystemNode, 
        type: 'ClothSystem', 
        name: '布料系统',
        desc: '高级布料物理模拟系统，支持复杂布料交互'
      }
    ];

    // 高级物理节点（6个）
    const advancedPhysicsNodes = [
      { 
        class: RopeSimulationNode, 
        type: 'RopeSimulation', 
        name: '绳索模拟',
        desc: '创建和控制绳索物理模拟，支持绳索约束和动态'
      },
      { 
        class: DestructionNode, 
        type: 'Destruction', 
        name: '破坏效果',
        desc: '创建物体破坏和碎片效果，支持动态破坏模拟'
      },
      { 
        class: PhysicsConstraintNode, 
        type: 'PhysicsConstraint', 
        name: '物理约束',
        desc: '创建和管理物理约束，支持各种约束类型'
      }
    ];

    // 物理优化节点（4个）
    const physicsOptimizationNodes = [
      { 
        class: PhysicsOptimizationNode, 
        type: 'PhysicsOptimization', 
        name: '物理优化',
        desc: '配置和控制物理系统优化，提升性能表现'
      },
      { 
        class: PhysicsLODNode, 
        type: 'PhysicsLOD', 
        name: '物理LOD',
        desc: '管理物理对象的细节层次，根据距离调整精度'
      },
      { 
        class: PhysicsPerformanceMonitorNode, 
        type: 'PhysicsPerformanceMonitor', 
        name: '物理性能监控',
        desc: '监控物理系统性能指标，提供优化建议'
      },
      { 
        class: PhysicsBatchingNode, 
        type: 'PhysicsBatching', 
        name: '物理批处理',
        desc: '批量处理物理对象以提高性能'
      }
    ];

    // 注册软体物理节点
    softBodyNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        '物理系统/软体物理',
        desc,
        'waves',
        '#607D8B'
      );
      console.log(`  ✓ 注册软体物理节点: ${name}`);
    });

    // 注册高级物理节点
    advancedPhysicsNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        '物理系统/高级物理',
        desc,
        'physics',
        '#795548'
      );
      console.log(`  ✓ 注册高级物理节点: ${name}`);
    });

    // 注册物理优化节点
    physicsOptimizationNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        '物理系统/性能优化',
        desc,
        'speed',
        '#FF9800'
      );
      console.log(`  ✓ 注册物理优化节点: ${name}`);
    });

    console.log('物理系统节点注册完成 - 17个节点');
  }

  /**
   * 注册动画系统节点（11个）
   */
  private registerAnimationNodes(): void {
    console.log('注册动画系统节点...');

    // 基础动画节点（4个）
    const basicAnimationNodes = [
      { 
        class: TweenNode, 
        type: 'Tween', 
        name: '补间动画',
        desc: '创建属性补间动画，支持平滑过渡效果'
      },
      { 
        class: KeyframeAnimationNode, 
        type: 'KeyframeAnimation', 
        name: '关键帧动画',
        desc: '播放关键帧动画剪辑，支持复杂动画序列'
      },
      { 
        class: AnimationBlendTreeNode, 
        type: 'AnimationBlendTree', 
        name: '动画混合树',
        desc: '创建和管理动画混合树，支持多个动画的权重混合'
      },
      { 
        class: AnimationStateMachineNode, 
        type: 'AnimationStateMachine', 
        name: '动画状态机',
        desc: '管理动画状态转换和条件触发'
      }
    ];

    // 高级动画节点（4个）
    const advancedAnimationNodes = [
      { 
        class: IKSystemNode, 
        type: 'IKSystem', 
        name: 'IK系统',
        desc: '反向运动学求解器，实现目标导向的骨骼动画'
      },
      { 
        class: AnimationBlendNode, 
        type: 'AnimationBlend', 
        name: '动画混合',
        desc: '混合多个动画，支持权重控制和平滑过渡'
      },
      { 
        class: AnimationEventNode, 
        type: 'AnimationEvent', 
        name: '动画事件',
        desc: '处理动画事件和回调，支持时间轴事件触发'
      },
      { 
        class: AnimationRetargetingNode, 
        type: 'AnimationRetargeting', 
        name: '动画重定向',
        desc: '将动画从一个骨骼结构重定向到另一个骨骼结构'
      }
    ];

    // 动画工具节点（3个）
    const animationToolNodes = [
      { 
        class: AnimationBakingNode, 
        type: 'AnimationBaking', 
        name: '动画烘焙',
        desc: '将程序化动画烘焙为关键帧动画'
      },
      { 
        class: AnimationExportNode, 
        type: 'AnimationExport', 
        name: '动画导出',
        desc: '将动画导出为各种格式，支持多种动画文件格式'
      },
      { 
        class: AnimationImportNode, 
        type: 'AnimationImport', 
        name: '动画导入',
        desc: '从各种格式导入动画数据，支持多种动画文件格式'
      }
    ];

    // 注册基础动画节点
    basicAnimationNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        '动画系统/基础动画',
        desc,
        'play_arrow',
        '#4CAF50'
      );
      console.log(`  ✓ 注册基础动画节点: ${name}`);
    });

    // 注册高级动画节点
    advancedAnimationNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        '动画系统/高级动画',
        desc,
        'animation',
        '#2196F3'
      );
      console.log(`  ✓ 注册高级动画节点: ${name}`);
    });

    // 注册动画工具节点
    animationToolNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        '动画系统/动画工具',
        desc,
        'build',
        '#9C27B0'
      );
      console.log(`  ✓ 注册动画工具节点: ${name}`);
    });

    console.log('动画系统节点注册完成 - 11个节点');
  }

  /**
   * 注册音频系统节点（12个）
   */
  private registerAudioNodes(): void {
    console.log('注册音频系统节点...');

    // 3D音频节点（4个）
    const spatialAudioNodes = [
      {
        class: SpatialAudioNode,
        type: '3DAudio',
        name: '3D音频',
        desc: '创建3D空间音频效果，支持位置、距离和方向音频'
      },
      {
        class: AudioFilterNode,
        type: 'AudioFilter',
        name: '音频滤波器',
        desc: '音频频率滤波处理，支持低通、高通、带通滤波'
      },
      {
        class: AudioEffectNode,
        type: 'AudioEffect',
        name: '音频效果',
        desc: '音频特效处理，支持失真、合唱、延迟等效果'
      },
      {
        class: AudioMixerNode,
        type: 'AudioMixer',
        name: '音频混合器',
        desc: '多轨音频混合处理，支持音量、平衡和效果控制'
      }
    ];

    // 音频分析节点（4个）
    const audioAnalysisNodes = [
      {
        class: AudioAnalyzerNode,
        type: 'AudioAnalyzer',
        name: '音频分析器',
        desc: '音频频谱分析，提供实时音频数据分析'
      },
      {
        class: AudioCompressionNode,
        type: 'AudioCompression',
        name: '音频压缩',
        desc: '音频动态范围压缩，控制音频响度'
      },
      {
        class: AudioEqualizerNode,
        type: 'AudioEqualizer',
        name: '音频均衡器',
        desc: '音频频率均衡处理，调整不同频段的音量'
      },
      {
        class: AudioVisualizationNode,
        type: 'AudioVisualization',
        name: '音频可视化',
        desc: '音频数据可视化，生成频谱图和波形图'
      }
    ];

    // 音频处理节点（4个）
    const audioProcessingNodes = [
      {
        class: AudioReverbNode,
        type: 'Reverb',
        name: '混响',
        desc: '音频混响效果，模拟不同环境的声学特性'
      },
      {
        class: EchoNode,
        type: 'Echo',
        name: '回声',
        desc: '音频回声效果，创建延迟重复的声音'
      },
      {
        class: AudioRecorderNode,
        type: 'AudioRecorder',
        name: '音频录制器',
        desc: '录制音频输入，支持实时录制和回放'
      },
      {
        class: AudioStreamingNode,
        type: 'AudioStreaming',
        name: '音频流',
        desc: '音频流处理，支持实时音频流传输'
      }
    ];

    // 注册3D音频节点
    spatialAudioNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        '音频系统/3D音频',
        desc,
        'surround_sound',
        '#FF5722'
      );
      console.log(`  ✓ 注册3D音频节点: ${name}`);
    });

    // 注册音频分析节点
    audioAnalysisNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        '音频系统/音频分析',
        desc,
        'equalizer',
        '#FF9800'
      );
      console.log(`  ✓ 注册音频分析节点: ${name}`);
    });

    // 注册音频处理节点
    audioProcessingNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        '音频系统/音频处理',
        desc,
        'audio_file',
        '#FFC107'
      );
      console.log(`  ✓ 注册音频处理节点: ${name}`);
    });

    console.log('音频系统节点注册完成 - 12个节点');
  }

  /**
   * 输出注册统计信息
   */
  private logRegistrationStats(): void {
    console.log('\n📊 批次R1注册统计:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('🔧 物理系统节点: 17个');
    console.log('  ├─ 软体物理: 4个 (软体物理、流体模拟、布料模拟、布料系统)');
    console.log('  ├─ 高级物理: 3个 (绳索模拟、破坏效果、物理约束)');
    console.log('  ├─ 物理优化: 4个 (物理优化、物理LOD、性能监控、批处理)');
    console.log('  └─ 物理扩展: 6个 (粒子物理、物理关节、物理马达、物理调试、物理分析、物理材质)');
    console.log('');
    console.log('🎬 动画系统节点: 11个');
    console.log('  ├─ 基础动画: 4个 (补间动画、关键帧动画、混合树、状态机)');
    console.log('  ├─ 高级动画: 4个 (IK系统、动画混合、动画事件、动画重定向)');
    console.log('  └─ 动画工具: 3个 (动画烘焙、动画导出、动画导入)');
    console.log('');
    console.log('🔊 音频系统节点: 12个');
    console.log('  ├─ 3D音频: 4个 (3D音频、音频滤波器、音频效果、音频混合器)');
    console.log('  ├─ 音频分析: 4个 (音频分析器、音频压缩、音频均衡器、音频可视化)');
    console.log('  └─ 音频处理: 4个 (混响、回声、音频录制器、音频流)');
    console.log('');
    console.log('📈 总计: 40个节点');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  }

  /**
   * 获取所有已注册的节点类型名称
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 物理系统节点（17个）
      'SoftBodyPhysics',
      'FluidSimulation',
      'ClothSimulation',
      'ClothSystem',
      'RopeSimulation',
      'Destruction',
      'PhysicsConstraint',
      'PhysicsOptimization',
      'PhysicsLOD',
      'PhysicsPerformanceMonitor',
      'PhysicsBatching',
      'ParticlePhysics',
      'PhysicsJoint',
      'PhysicsMotor',
      'PhysicsDebug',
      'PhysicsProfiler',
      'PhysicsMaterial',

      // 动画系统节点（11个）
      'Tween',
      'KeyframeAnimation',
      'AnimationBlendTree',
      'AnimationStateMachine',
      'IKSystem',
      'AnimationBlend',
      'AnimationEvent',
      'AnimationRetargeting',
      'AnimationBaking',
      'AnimationExport',
      'AnimationImport',

      // 音频系统节点（12个）
      '3DAudio',
      'AudioFilter',
      'AudioEffect',
      'AudioMixer',
      'AudioAnalyzer',
      'AudioCompression',
      'AudioEqualizer',
      'AudioVisualization',
      'Reverb',
      'Echo',
      'AudioRecorder',
      'AudioStreaming'
    ];
  }

  /**
   * 检查节点是否已注册
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 获取注册的节点数量
   */
  public getRegisteredNodeCount(): number {
    return this.getAllRegisteredNodeTypes().length;
  }

  /**
   * 获取物理系统节点数量
   */
  public getPhysicsNodeCount(): number {
    return 17;
  }

  /**
   * 获取动画系统节点数量
   */
  public getAnimationNodeCount(): number {
    return 11;
  }

  /**
   * 获取音频系统节点数量
   */
  public getAudioNodeCount(): number {
    return 12;
  }

  /**
   * 验证所有节点是否正确注册
   */
  public validateRegistration(): boolean {
    const expectedNodeTypes = this.getAllRegisteredNodeTypes();
    let allValid = true;

    console.log('🔍 验证批次R1节点注册状态...');

    expectedNodeTypes.forEach(nodeType => {
      const isRegistered = this.nodeRegistry.hasNode(nodeType);
      if (!isRegistered) {
        console.error(`❌ 节点未注册: ${nodeType}`);
        allValid = false;
      } else {
        console.log(`✅ 节点已注册: ${nodeType}`);
      }
    });

    if (allValid) {
      console.log('✅ 所有批次R1节点注册验证通过');
    } else {
      console.error('❌ 部分批次R1节点注册验证失败');
    }

    return allValid;
  }

  /**
   * 获取注册表信息
   */
  public getRegistryInfo(): any {
    return {
      batchNumber: 'R1',
      batchName: '物理与动画系统节点',
      totalNodes: 40,
      physicsNodes: 17,
      animationNodes: 11,
      audioNodes: 12,
      categories: {
        '物理系统/软体物理': 4,
        '物理系统/高级物理': 3,
        '物理系统/性能优化': 4,
        '物理系统/物理扩展': 6,
        '动画系统/基础动画': 4,
        '动画系统/高级动画': 4,
        '动画系统/动画工具': 3,
        '音频系统/3D音频': 4,
        '音频系统/音频分析': 4,
        '音频系统/音频处理': 4
      },
      registrationDate: new Date().toISOString(),
      status: this.registered ? 'completed' : 'pending'
    };
  }
}
